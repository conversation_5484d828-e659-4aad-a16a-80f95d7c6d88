/**
 * lucide-react v0.0.1 - ISC
 */

import createLucideIcon from '../createLucideIcon.mjs';

const MonitorStop = createLucideIcon("MonitorStop", [
  ["rect", { x: "9", y: "7", width: "6", height: "6", key: "4xvc6r" }],
  [
    "rect",
    { width: "20", height: "14", x: "2", y: "3", rx: "2", key: "48i651" }
  ],
  ["path", { d: "M12 17v4", key: "1riwvh" }],
  ["path", { d: "M8 21h8", key: "1ev6f3" }]
]);

export { MonitorStop as default };
//# sourceMappingURL=monitor-stop.mjs.map
