# Lang<PERSON>hain Chat Application

一个基于 LangChain 和 FastAPI 的智能对话应用，支持多模型配置、文件上传、图片处理和流式输出。

## 功能特性

- 🤖 支持多种大模型 API（OpenAI、Azure OpenAI、Ollama 等兼容接口）
- 🔧 可自定义 API Key、Base URL 和模型名称
- 📁 支持文件上传（.txt、.pdf 等）和 RAG 检索
- 🖼️ 支持图片上传和多模态对话
- 💬 完整的对话历史记录
- ⚡ 实时流式输出
- 🎨 现代化的 Web 界面

## 技术栈

### 后端
- FastAPI - 高性能 Web 框架
- LangChain - LLM 应用开发框架
- FAISS - 向量数据库
- PyPDF - PDF 文档解析

### 前端
- React - 用户界面框架
- Vite - 构建工具
- Tailwind CSS - 样式框架

## 快速开始

### 后端启动

```bash
cd backend
pip install -r requirements.txt
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

### 前端启动

```bash
cd frontend
npm install
npm run dev
```

访问 http://localhost:5173 开始使用。

## 项目结构

```
├── backend/                 # 后端代码
│   ├── main.py             # FastAPI 主应用
│   ├── models/             # 数据模型
│   ├── services/           # 业务逻辑
│   └── requirements.txt    # Python 依赖
├── frontend/               # 前端代码
│   ├── src/               # React 源码
│   ├── public/            # 静态资源
│   └── package.json       # Node.js 依赖
└── README.md              # 项目说明
```

## 使用说明

1. 配置模型参数（API Key、Base URL、模型名称）
2. 上传文档文件（可选，用于 RAG 检索）
3. 开始对话，支持文本和图片输入
4. 享受流式输出的实时体验

## 许可证

MIT License
