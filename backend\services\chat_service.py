import os
import tempfile
import uuid
from typing import Dict, List, Optional, AsyncGenerator
from datetime import datetime

from langchain_openai import ChatOpenAI, OpenAIEmbeddings
from langchain_core.prompts import Chat<PERSON>romptTemplate, MessagesPlaceholder
from langchain_core.runnables import RunnableParallel, RunnablePassthrough
from langchain_core.runnables.history import RunnableWith<PERSON>essageHistory
from langchain_core.chat_history import InMemoryChatMessageHistory
from langchain_core.messages import HumanMessage, AIMessage
from langchain_community.document_loaders import PyPDFLoader, TextLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_community.vectorstores import FAISS

from models.schemas import ModelConfig, ChatMessage


class ChatService:
    """聊天服务类"""
    
    def __init__(self):
        # 内存存储（生产环境建议使用 Redis）
        self.session_histories: Dict[str, InMemoryChatMessageHistory] = {}
        self.session_vstores: Dict[str, FAISS] = {}
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=800, 
            chunk_overlap=100
        )
    
    def get_history(self, session_id: str) -> InMemoryChatMessageHistory:
        """获取会话历史"""
        if session_id not in self.session_histories:
            self.session_histories[session_id] = InMemoryChatMessageHistory()
        return self.session_histories[session_id]
    
    def get_model(self, config: ModelConfig) -> ChatOpenAI:
        """创建聊天模型实例"""
        kwargs = {
            "api_key": config.api_key,
            "model": config.model,
            "temperature": config.temperature,
            "streaming": True,
        }
        
        if config.base_url:
            kwargs["base_url"] = config.base_url
        
        if config.max_tokens:
            kwargs["max_tokens"] = config.max_tokens
            
        return ChatOpenAI(**kwargs)
    
    def get_embeddings(self, config: ModelConfig) -> OpenAIEmbeddings:
        """创建嵌入模型实例"""
        kwargs = {
            "api_key": config.api_key,
            "model": "text-embedding-3-small"
        }
        
        if config.base_url:
            kwargs["base_url"] = config.base_url
            
        return OpenAIEmbeddings(**kwargs)
    
    def ensure_vstore(self, session_id: str, config: ModelConfig) -> FAISS:
        """确保向量存储存在"""
        if session_id not in self.session_vstores:
            embeddings = self.get_embeddings(config)
            # 创建空的向量存储
            self.session_vstores[session_id] = FAISS.from_texts(
                [""], embeddings
            )
            # 清空初始化的空向量
            self.session_vstores[session_id].index.reset()
        return self.session_vstores[session_id]
    
    def format_docs(self, docs) -> str:
        """格式化文档"""
        return "\n\n".join([d.page_content for d in docs])
    
    async def upload_documents(
        self, 
        session_id: str, 
        config: ModelConfig, 
        file_contents: List[tuple]
    ) -> int:
        """上传并处理文档"""
        vstore = self.ensure_vstore(session_id, config)
        embeddings = self.get_embeddings(config)
        
        added_chunks = 0
        all_chunks = []
        
        for filename, content in file_contents:
            suffix = os.path.splitext(filename)[1].lower()
            
            # 创建临时文件
            with tempfile.NamedTemporaryFile(delete=False, suffix=suffix) as tmp:
                tmp.write(content)
                tmp_path = tmp.name
            
            try:
                # 根据文件类型加载文档
                if suffix == ".pdf":
                    pages = PyPDFLoader(tmp_path).load()
                elif suffix in [".txt", ".md"]:
                    pages = TextLoader(tmp_path, encoding="utf-8").load()
                else:
                    continue  # 跳过不支持的文件类型
                
                # 分割文档
                chunks = self.text_splitter.split_documents(pages)
                all_chunks.extend(chunks)
                added_chunks += len(chunks)
                
            finally:
                # 清理临时文件
                os.remove(tmp_path)
        
        # 如果有新文档，更新向量存储
        if all_chunks:
            if len(vstore.docstore._dict) == 0:
                # 如果是空的向量存储，直接创建
                self.session_vstores[session_id] = FAISS.from_documents(
                    all_chunks, embeddings
                )
            else:
                # 合并到现有向量存储
                new_vs = FAISS.from_documents(all_chunks, embeddings)
                vstore.merge_from(new_vs)
        
        return added_chunks
    
    async def chat_stream(
        self, 
        session_id: str, 
        message: str, 
        config: ModelConfig,
        use_rag: bool = True,
        images: Optional[List[str]] = None
    ) -> AsyncGenerator[str, None]:
        """流式聊天"""
        history = self.get_history(session_id)
        model = self.get_model(config)
        
        # 如果有图片，使用多模态对话
        if images:
            async for chunk in self._multimodal_chat_stream(
                model, history, message, images
            ):
                yield chunk
            return
        
        # 如果启用 RAG 且有向量存储
        if (use_rag and 
            session_id in self.session_vstores and 
            len(self.session_vstores[session_id].docstore._dict) > 0):
            
            async for chunk in self._rag_chat_stream(
                session_id, model, history, message
            ):
                yield chunk
        else:
            # 普通文本对话
            async for chunk in self._simple_chat_stream(
                model, history, message
            ):
                yield chunk
    
    async def _multimodal_chat_stream(
        self, 
        model: ChatOpenAI, 
        history: InMemoryChatMessageHistory,
        message: str, 
        images: List[str]
    ) -> AsyncGenerator[str, None]:
        """多模态聊天流"""
        # 构建消息列表
        msgs = list(history.messages)
        
        # 构建多模态内容
        content_parts = [{"type": "text", "text": message}]
        for img in images:
            content_parts.append({
                "type": "image_url",
                "image_url": {"url": img}
            })
        
        msgs.append(HumanMessage(content=content_parts))
        
        # 流式输出
        full_response = ""
        async for chunk in model.astream(msgs):
            content = chunk.content or ""
            full_response += content
            yield content
        
        # 更新历史
        history.add_user_message(message)
        history.add_message(AIMessage(content=full_response))
    
    async def _rag_chat_stream(
        self, 
        session_id: str, 
        model: ChatOpenAI,
        history: InMemoryChatMessageHistory, 
        message: str
    ) -> AsyncGenerator[str, None]:
        """RAG 聊天流"""
        retriever = self.session_vstores[session_id].as_retriever(
            search_kwargs={"k": 4}
        )
        
        prompt = ChatPromptTemplate.from_messages([
            ("system", "你是一个有用的助手。如果提供的上下文信息有帮助，请使用它来回答问题：\n{context}"),
            MessagesPlaceholder("history"),
            ("human", "{input}"),
        ])
        
        chain = (
            RunnableParallel(
                context=retriever | self.format_docs,
                history=lambda x: history.messages,
                input=RunnablePassthrough()
            )
            | prompt
            | model
        )
        
        stateful_chain = RunnableWithMessageHistory(
            chain,
            lambda sid: self.get_history(sid),
            input_messages_key="input",
            history_messages_key="history",
        )
        
        # 流式输出
        async for chunk in stateful_chain.astream(
            message,
            config={"configurable": {"session_id": session_id}},
        ):
            yield chunk.content or ""
    
    async def _simple_chat_stream(
        self, 
        model: ChatOpenAI,
        history: InMemoryChatMessageHistory, 
        message: str
    ) -> AsyncGenerator[str, None]:
        """简单聊天流"""
        msgs = list(history.messages)
        msgs.append(HumanMessage(content=message))
        
        # 流式输出
        full_response = ""
        async for chunk in model.astream(msgs):
            content = chunk.content or ""
            full_response += content
            yield content
        
        # 更新历史
        history.add_user_message(message)
        history.add_message(AIMessage(content=full_response))
    
    def get_session_history(self, session_id: str) -> List[ChatMessage]:
        """获取会话历史"""
        if session_id not in self.session_histories:
            return []
        
        messages = []
        for msg in self.session_histories[session_id].messages:
            messages.append(ChatMessage(
                role="user" if isinstance(msg, HumanMessage) else "assistant",
                content=msg.content if isinstance(msg.content, str) else str(msg.content),
                timestamp=datetime.now().isoformat()
            ))
        
        return messages
    
    def clear_session(self, session_id: str) -> bool:
        """清除会话"""
        cleared = False
        if session_id in self.session_histories:
            del self.session_histories[session_id]
            cleared = True
        if session_id in self.session_vstores:
            del self.session_vstores[session_id]
            cleared = True
        return cleared
