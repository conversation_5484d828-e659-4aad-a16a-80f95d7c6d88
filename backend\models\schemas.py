from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
import uuid


class ModelConfig(BaseModel):
    """模型配置"""
    api_key: str
    base_url: Optional[str] = None
    model: str = "gpt-4o-mini"
    temperature: float = Field(default=0.7, ge=0.0, le=2.0)
    max_tokens: Optional[int] = Field(default=None, gt=0)


class ChatMessage(BaseModel):
    """聊天消息"""
    role: str  # "user" or "assistant"
    content: str
    timestamp: Optional[str] = None


class ChatRequest(BaseModel):
    """聊天请求"""
    session_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    message: str
    use_rag: bool = True
    images: Optional[List[str]] = None  # base64 data URLs 或公网 URL
    model_config: ModelConfig


class ChatResponse(BaseModel):
    """聊天响应"""
    session_id: str
    message: str
    timestamp: str


class UploadResponse(BaseModel):
    """文件上传响应"""
    success: bool
    session_id: str
    chunks_added: int
    message: str


class SessionHistoryResponse(BaseModel):
    """会话历史响应"""
    session_id: str
    messages: List[ChatMessage]


class ErrorResponse(BaseModel):
    """错误响应"""
    error: str
    detail: Optional[str] = None


class HealthResponse(BaseModel):
    """健康检查响应"""
    status: str
    message: str
