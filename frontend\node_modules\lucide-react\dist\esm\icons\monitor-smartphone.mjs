/**
 * lucide-react v0.0.1 - ISC
 */

import createLucideIcon from '../createLucideIcon.mjs';

const MonitorSmartphone = createLucideIcon("MonitorSmartphone", [
  [
    "path",
    {
      d: "M18 8V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v7a2 2 0 0 0 2 2h8",
      key: "10dyio"
    }
  ],
  ["path", { d: "M10 19v-3.96 3.15", key: "1irgej" }],
  ["path", { d: "M7 19h5", key: "qswx4l" }],
  [
    "rect",
    { width: "6", height: "10", x: "16", y: "12", rx: "2", key: "1egngj" }
  ]
]);

export { MonitorSmartphone as default };
//# sourceMappingURL=monitor-smartphone.mjs.map
