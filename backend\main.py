import os
import json
from datetime import datetime
from typing import List

from fastapi import FastAPI, UploadFile, File, HTTPException, Form
from fastapi.responses import StreamingResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import ValidationError

from models.schemas import (
    ChatRequest, ChatResponse, UploadResponse, 
    SessionHistoryResponse, ErrorResponse, HealthResponse,
    ModelConfig
)
from services.chat_service import ChatService

# 创建 FastAPI 应用
app = FastAPI(
    title="LangChain Chat API",
    description="基于 LangChain 的智能对话 API",
    version="1.0.0"
)

# 配置 CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境应该设置具体的域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 创建聊天服务实例
chat_service = ChatService()


@app.get("/", response_model=HealthResponse)
async def root():
    """根路径健康检查"""
    return HealthResponse(
        status="healthy",
        message="LangChain Chat API is running"
    )


@app.get("/health", response_model=HealthResponse)
async def health_check():
    """健康检查"""
    return HealthResponse(
        status="healthy",
        message="Service is running normally"
    )


@app.post("/upload/docs")
async def upload_documents(
    session_id: str = Form(...),
    model_config: str = Form(...),  # JSON 字符串
    files: List[UploadFile] = File(...)
):
    """上传文档文件"""
    try:
        # 解析模型配置
        config_dict = json.loads(model_config)
        config = ModelConfig(**config_dict)
        
        # 读取文件内容
        file_contents = []
        for file in files:
            content = await file.read()
            file_contents.append((file.filename, content))
        
        # 处理文档
        chunks_added = await chat_service.upload_documents(
            session_id, config, file_contents
        )
        
        return UploadResponse(
            success=True,
            session_id=session_id,
            chunks_added=chunks_added,
            message=f"Successfully uploaded {len(files)} files, added {chunks_added} chunks"
        )
        
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=f"Invalid model config: {e}")
    except json.JSONDecodeError:
        raise HTTPException(status_code=400, detail="Invalid JSON in model_config")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Upload failed: {str(e)}")


@app.post("/chat/stream")
async def chat_stream(request: ChatRequest):
    """流式聊天接口"""
    try:
        async def generate():
            async for chunk in chat_service.chat_stream(
                session_id=request.session_id,
                message=request.message,
                config=request.model_config,
                use_rag=request.use_rag,
                images=request.images
            ):
                yield chunk
        
        return StreamingResponse(
            generate(), 
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
            }
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Chat failed: {str(e)}")


@app.post("/chat")
async def chat(request: ChatRequest):
    """非流式聊天接口"""
    try:
        full_response = ""
        async for chunk in chat_service.chat_stream(
            session_id=request.session_id,
            message=request.message,
            config=request.model_config,
            use_rag=request.use_rag,
            images=request.images
        ):
            full_response += chunk
        
        return ChatResponse(
            session_id=request.session_id,
            message=full_response,
            timestamp=datetime.now().isoformat()
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Chat failed: {str(e)}")


@app.get("/sessions/{session_id}/history", response_model=SessionHistoryResponse)
async def get_session_history(session_id: str):
    """获取会话历史"""
    try:
        messages = chat_service.get_session_history(session_id)
        return SessionHistoryResponse(
            session_id=session_id,
            messages=messages
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get history: {str(e)}")


@app.delete("/sessions/{session_id}")
async def clear_session(session_id: str):
    """清除会话"""
    try:
        cleared = chat_service.clear_session(session_id)
        return {
            "success": True,
            "session_id": session_id,
            "cleared": cleared,
            "message": "Session cleared successfully" if cleared else "Session not found"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to clear session: {str(e)}")


@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """HTTP 异常处理器"""
    return JSONResponse(
        status_code=exc.status_code,
        content=ErrorResponse(
            error=exc.detail,
            detail=f"Request: {request.method} {request.url}"
        ).dict()
    )


@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    """通用异常处理器"""
    return JSONResponse(
        status_code=500,
        content=ErrorResponse(
            error="Internal server error",
            detail=str(exc)
        ).dict()
    )


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app", 
        host="0.0.0.0", 
        port=8000, 
        reload=True,
        log_level="info"
    )
